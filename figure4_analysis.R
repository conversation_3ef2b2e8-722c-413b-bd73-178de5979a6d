#!/usr/bin/env Rscript
# Figure 4 Analysis Script - R Version
# 基于R语言的Figure 4分析脚本：3个Aggravation物质的小提琴图
# CAST、GALNS、3-hydroxypropanoic的四小提琴图
#
# Author: AI Assistant
# Date: 2025-08-03
# Version: 1.0

# =============================================================================
# 1. 环境准备和包加载
# =============================================================================

# 检查并安装必要的包
required_packages <- c(
  "readxl",        # Excel文件读取
  "dplyr",         # 数据处理
  "tibble",        # 数据框操作
  "tidyr",         # 数据重塑
  "stringr",       # 字符串处理
  "ggplot2",       # 基础绘图
  "ggpubr",        # 发表质量图表
  "cowplot",       # 图表组合
  "viridis",       # 颜色方案
  "scales"         # 坐标轴格式化
)

# 安装缺失的包
new_packages <- required_packages[!(required_packages %in% installed.packages()[,"Package"])]
if(length(new_packages)) {
  install.packages(new_packages, repos = "https://cran.rstudio.com/")
}

# 加载包
suppressPackageStartupMessages({
  library(readxl)
  library(dplyr)
  library(tibble)
  library(tidyr)
  library(ggplot2)
  library(ggpubr)
  library(cowplot)
  library(viridis)
  library(scales)
})

# =============================================================================
# 2. 数据加载和预处理
# =============================================================================

cat("正在加载数据...\n")

# 加载基线数据 (Figure 1: VH vs SRD+VH)
baseline_protein <- read_excel("基线蛋白组学(1-stage2_vs_1-stage1_annotation).xlsx")
baseline_metabolite <- read_excel("基线代谢组学（1-stage2_vs_1-stage1_info）.xlsx")
baseline_group <- read_excel("基线分组情况（1-stage2_vs_1-stage1_group）.xlsx")

# 加载纵向数据 (Figure 2: Post-Anti-VEGF vs Baseline)
longitudinal_protein <- read_excel("纵向蛋白组学（2_vs_1_annotation）.xlsx")
longitudinal_metabolite <- read_excel("纵向代谢组学（2_vs_1_info）.xlsx")
longitudinal_group <- read_excel("纵向分组情况（2_vs_1_group）.xlsx")

cat(sprintf("数据加载完成:\n"))
cat(sprintf("- 基线蛋白质数据: %d行 x %d列\n", nrow(baseline_protein), ncol(baseline_protein)))
cat(sprintf("- 基线代谢物数据: %d行 x %d列\n", nrow(baseline_metabolite), ncol(baseline_metabolite)))
cat(sprintf("- 纵向蛋白质数据: %d行 x %d列\n", nrow(longitudinal_protein), ncol(longitudinal_protein)))
cat(sprintf("- 纵向代谢物数据: %d行 x %d列\n", nrow(longitudinal_metabolite), ncol(longitudinal_metabolite)))

# =============================================================================
# 3. 设置绘图主题和颜色
# =============================================================================

# 设置期刊发表质量的主题
theme_publication <- theme_bw() +
  theme(
    text = element_text(size = 12),
    axis.title = element_text(size = 14, face = "bold"),
    axis.text = element_text(size = 12),
    plot.title = element_text(size = 16, face = "bold", hjust = 0.5),
    legend.text = element_text(size = 12),
    legend.title = element_text(size = 12, face = "bold"),
    panel.grid.major = element_line(color = "grey90", linewidth = 0.5),
    panel.grid.minor = element_blank(),
    panel.border = element_rect(color = "black", linewidth = 1),
    strip.background = element_rect(fill = "white", color = "black"),
    strip.text = element_text(size = 12, face = "bold")
  )

# 设置颜色方案 - 简化为两种颜色
colors <- list(
  vh = "#1F78B4",        # 蓝色 - Simple VH组（基线比较）
  srd_vh = "#1F78B4",    # 蓝色 - SRD+VH组（基线比较，与VH同色）
  baseline = "#E31A1C",  # 红色 - 基线（纵向比较）
  post_vegf = "#E31A1C", # 红色 - 治疗后（纵向比较，与基线同色）
  protein_bg = "#F0F8FF", # 淡蓝色背景 - 蛋白质
  metabolite_bg = "#F0FFF0" # 淡绿色背景 - 代谢物
)

cat("\n绘图环境设置完成\n")

# =============================================================================
# 4. 目标分子信息定义
# =============================================================================

# 定义目标分子
target_molecules <- list(
  CAST = list(
    type = "protein",
    gene_name = "CAST",
    accession = NULL,  # 需要从数据中查找
    title = "CAST"
  ),
  GALNS = list(
    type = "protein",
    gene_name = "GALNS",
    accession = NULL,  # 需要从数据中查找
    title = "GALNS"
  ),
  hydroxypropanoic = list(
    type = "metabolite",
    compound_name = "3-Hydroxypropanoic Acid",
    index = "MADN0081",  # 从figure1代码中获得
    title = "3-Hydroxypropanoic Acid"
  )
)

cat("目标分子定义完成\n")

# =============================================================================
# 5. 数据提取函数
# =============================================================================

# 提取蛋白质表达数据
extract_protein_data <- function(protein_data, group_data, gene_name) {
  
  # 查找目标蛋白质
  target_protein <- protein_data %>%
    filter(Gene == gene_name | grepl(gene_name, Gene, ignore.case = TRUE))
  
  if(nrow(target_protein) == 0) {
    cat(sprintf("警告：未找到蛋白质 %s\n", gene_name))
    return(NULL)
  }
  
  if(nrow(target_protein) > 1) {
    cat(sprintf("发现多个匹配的蛋白质 %s，使用第一个\n", gene_name))
    target_protein <- target_protein[1, ]
  }
  
  # 获取样本列（包含"--"的列）
  sample_cols <- grep("--", colnames(protein_data), value = TRUE)
  
  # 提取表达数据
  expression_data <- target_protein %>%
    select(all_of(sample_cols)) %>%
    pivot_longer(everything(), names_to = "Sample", values_to = "Expression") %>%
    mutate(Expression = as.numeric(Expression))
  
  # 添加分组信息
  expression_data <- expression_data %>%
    left_join(group_data, by = "Sample")
  
  return(expression_data)
}

# 提取代谢物表达数据
extract_metabolite_data <- function(metabolite_data, group_data, index_code) {
  
  # 查找目标代谢物
  target_metabolite <- metabolite_data %>%
    filter(Index == index_code)
  
  if(nrow(target_metabolite) == 0) {
    cat(sprintf("警告：未找到代谢物 %s\n", index_code))
    return(NULL)
  }
  
  # 获取样本列（包含"--"的列）
  sample_cols <- grep("--", colnames(metabolite_data), value = TRUE)
  
  # 提取表达数据
  expression_data <- target_metabolite %>%
    select(all_of(sample_cols)) %>%
    pivot_longer(everything(), names_to = "Sample", values_to = "Expression") %>%
    mutate(Expression = as.numeric(Expression))
  
  # 添加分组信息
  expression_data <- expression_data %>%
    left_join(group_data, by = "Sample")
  
  return(expression_data)
}

cat("数据提取函数创建完成\n")

# =============================================================================
# 6. 小提琴图创建函数
# =============================================================================

create_violin_plot <- function(molecule_info) {
  
  cat(sprintf("正在创建 %s 的小提琴图...\n", molecule_info$title))
  
  # 根据分子类型提取数据
  if(molecule_info$type == "protein") {
    # 提取基线数据 (VH vs SRD+VH)
    baseline_data <- extract_protein_data(baseline_protein, baseline_group, molecule_info$gene_name)
    # 提取纵向数据 (治疗前后)
    longitudinal_data <- extract_protein_data(longitudinal_protein, longitudinal_group, molecule_info$gene_name)
  } else {
    # 提取基线数据 (VH vs SRD+VH)
    baseline_data <- extract_metabolite_data(baseline_metabolite, baseline_group, molecule_info$index)
    # 提取纵向数据 (治疗前后)
    longitudinal_data <- extract_metabolite_data(longitudinal_metabolite, longitudinal_group, molecule_info$index)
  }
  
  if(is.null(baseline_data) || is.null(longitudinal_data)) {
    cat(sprintf("跳过 %s：数据不完整\n", molecule_info$title))
    return(NULL)
  }
  
  # 准备基线数据 (VH vs SRD+VH)
  baseline_plot_data <- baseline_data %>%
    mutate(
      Group_Label = case_when(
        Group == "1-stage1" ~ "Simple VH",
        Group == "1-stage2" ~ "SRD+VH",
        TRUE ~ as.character(Group)
      ),
      Comparison = "Baseline Comparison",
      Violin_Group = paste(Comparison, Group_Label, sep = "_")
    )

  # 准备纵向数据 (治疗前后)
  longitudinal_plot_data <- longitudinal_data %>%
    mutate(
      Group = as.character(Group),  # 确保Group列是字符型
      Group_Label = case_when(
        Group == "1" ~ "Baseline",
        Group == "2" ~ "Post-anti-VEGF",
        TRUE ~ as.character(Group)
      ),
      Comparison = "Longitudinal Comparison",
      Violin_Group = paste(Comparison, Group_Label, sep = "_")
    )
  
  # 合并数据
  combined_data <- bind_rows(baseline_plot_data, longitudinal_plot_data)

  # 为每个样本添加固定的jitter偏移
  # 提取患者ID（对于纵向数据）或使用样本ID（对于基线数据）
  combined_data <- combined_data %>%
    mutate(
      Patient_ID = case_when(
        grepl("--[12]$", Sample) ~ gsub("--[12]$", "", Sample),  # 纵向数据：提取患者ID
        TRUE ~ Sample  # 基线数据：直接使用样本ID
      ),
      # 基于患者ID生成固定的jitter偏移（-0.2到0.2之间）
      # 使用hash函数确保更均匀的分布
      jitter_offset = (((as.numeric(as.factor(Patient_ID)) * **********) %% 1000) / 1000 - 0.5) * 0.4
    )

  # 设置因子顺序
  combined_data$Violin_Group <- factor(combined_data$Violin_Group,
                                      levels = c("Baseline Comparison_Simple VH", "Baseline Comparison_SRD+VH",
                                                "Longitudinal Comparison_Baseline", "Longitudinal Comparison_Post-anti-VEGF"))

  # 重新计算x_position（在因子设置之后）
  combined_data <- combined_data %>%
    mutate(x_position = as.numeric(Violin_Group) + jitter_offset)

  # 计算统计检验
  # 1. 基线比较：Simple VH vs SRD+VH (独立t检验)
  baseline_vh <- baseline_plot_data %>% filter(Group_Label == "Simple VH") %>% pull(Expression)
  baseline_srd <- baseline_plot_data %>% filter(Group_Label == "SRD+VH") %>% pull(Expression)
  baseline_ttest <- t.test(baseline_vh, baseline_srd, var.equal = FALSE)
  baseline_pval <- baseline_ttest$p.value

  # 计算基线比较趋势 (SRD+VH vs Simple VH)
  baseline_mean_vh <- mean(baseline_vh, na.rm = TRUE)
  baseline_mean_srd <- mean(baseline_srd, na.rm = TRUE)
  baseline_trend <- if(baseline_mean_srd > baseline_mean_vh) "up" else "down"

  # 2. 纵向比较：Baseline vs Post-anti-VEGF (配对t检验)
  # 准备配对数据 - 使用更直接的方法
  baseline_values <- longitudinal_plot_data %>%
    filter(Group_Label == "Baseline") %>%
    mutate(Patient_ID = gsub("--1$", "", Sample)) %>%
    select(Patient_ID, Expression) %>%
    rename(Baseline_Expression = Expression)

  post_values <- longitudinal_plot_data %>%
    filter(Group_Label == "Post-anti-VEGF") %>%
    mutate(Patient_ID = gsub("--2$", "", Sample)) %>%
    select(Patient_ID, Expression) %>%
    rename(Post_Expression = Expression)

  # 合并配对数据
  paired_data <- inner_join(baseline_values, post_values, by = "Patient_ID") %>%
    filter(!is.na(Baseline_Expression) & !is.na(Post_Expression))

  # 调试信息
  cat(sprintf("配对样本数量: %d\n", nrow(paired_data)))

  # 检查是否有足够的配对样本进行t检验
  if(nrow(paired_data) >= 2) {
    longitudinal_ttest <- t.test(paired_data$Baseline_Expression, paired_data$Post_Expression, paired = TRUE)
    longitudinal_pval <- longitudinal_ttest$p.value

    # 计算纵向比较趋势 (Post-anti-VEGF vs Baseline)
    longitudinal_mean_baseline <- mean(paired_data$Baseline_Expression, na.rm = TRUE)
    longitudinal_mean_post <- mean(paired_data$Post_Expression, na.rm = TRUE)
    longitudinal_trend <- if(longitudinal_mean_post > longitudinal_mean_baseline) "up" else "down"
  } else {
    cat("警告：配对样本数量不足，跳过配对t检验\n")
    longitudinal_pval <- NA
    longitudinal_trend <- "N/A"
  }

  # 格式化P值和趋势
  format_pval_with_trend <- function(p, trend) {
    if (is.na(p)) return("N/A")
    else if (p < 0.001) return(sprintf("%s, p < 0.001", trend))
    else return(sprintf("%s, p = %.3f", trend, p))
  }

  baseline_pval_text <- format_pval_with_trend(baseline_pval, baseline_trend)
  longitudinal_pval_text <- format_pval_with_trend(longitudinal_pval, longitudinal_trend)

  # 设置背景色
  bg_color <- if(molecule_info$type == "protein") colors$protein_bg else colors$metabolite_bg

  # 创建小提琴图
  p <- ggplot(combined_data, aes(x = Violin_Group, y = Expression, fill = Group_Label)) +
    geom_violin(alpha = 0.7, trim = FALSE) +
    geom_boxplot(width = 0.1, alpha = 0.8, outlier.shape = NA) +
    geom_point(aes(x = x_position), alpha = 0.8, size = 2.5,
               color = "black", stroke = 0.3, shape = 21) +
    scale_fill_manual(values = c(
      "Simple VH" = colors$vh,
      "SRD+VH" = colors$srd_vh,
      "Baseline" = colors$baseline,
      "Post-anti-VEGF" = colors$post_vegf
    )) +
    scale_x_discrete(labels = c("Simple VH", "SRD+VH", "Baseline", "Post-anti-VEGF")) +
    labs(
      title = molecule_info$title,
      x = "",
      y = "Expression Level",
      fill = "Group"
    ) +
    theme_publication +
    theme(
      axis.text.x = element_text(angle = 45, hjust = 1),
      legend.position = "none",  # 移除图例，在最终图中统一添加
      panel.background = element_rect(fill = bg_color, color = NA),
      plot.background = element_rect(fill = bg_color, color = NA),
      # 调整plot边距：上边距增加，下边距进一步减少
      plot.margin = margin(t = 15, r = 5, b = 2, l = 5, unit = "pt")
    )

  # 添加分组分隔线
  p <- p + geom_vline(xintercept = 2.5, linetype = "dashed", alpha = 0.5, color = "gray50")

  # 计算Y轴范围用于P值位置
  y_range <- range(combined_data$Expression, na.rm = TRUE)
  y_max <- y_range[2]
  y_min <- y_range[1]
  y_span <- y_max - y_min

  # 设置P值的Y位置 - 图表绘图区域的真正顶端
  pval_y_position <- y_max + 0.05 * y_span

  # 添加P值注释 - 放在固定的顶部位置
  # 基线比较的P值 (Simple VH vs SRD+VH)
  p <- p + annotate("text", x = 1.5, y = pval_y_position,
                    label = baseline_pval_text, size = 3.5, hjust = 0.5,
                    color = "black", fontface = "bold")

  # 纵向比较的P值 (Baseline vs Post-anti-VEGF)
  p <- p + annotate("text", x = 3.5, y = pval_y_position,
                    label = longitudinal_pval_text, size = 3.5, hjust = 0.5,
                    color = "black", fontface = "bold")

  # 为纵向数据添加配对连线
  # 调试：查看纵向数据结构
  cat("纵向数据样本数量:\n")
  cat(sprintf("  Baseline: %d\n", sum(longitudinal_plot_data$Group_Label == "Baseline")))
  cat(sprintf("  Post-anti-VEGF: %d\n", sum(longitudinal_plot_data$Group_Label == "Post-anti-VEGF")))

  # 提取纵向数据中的配对样本
  baseline_samples <- longitudinal_plot_data %>%
    filter(Group_Label == "Baseline") %>%
    select(Sample, Expression) %>%
    mutate(Patient_ID = gsub("--1$", "", Sample)) %>%  # 提取患者ID
    rename(Expression_Baseline = Expression)

  post_samples <- longitudinal_plot_data %>%
    filter(Group_Label == "Post-anti-VEGF") %>%
    select(Sample, Expression) %>%
    mutate(Patient_ID = gsub("--2$", "", Sample)) %>%  # 提取患者ID
    rename(Expression_Post = Expression)

  # 调试：查看样本ID
  if(nrow(baseline_samples) > 0 && nrow(post_samples) > 0) {
    cat("Baseline样本ID示例:", head(baseline_samples$Sample, 3), "\n")
    cat("Post样本ID示例:", head(post_samples$Sample, 3), "\n")
    cat("Baseline患者ID示例:", head(baseline_samples$Patient_ID, 3), "\n")
    cat("Post患者ID示例:", head(post_samples$Patient_ID, 3), "\n")
  }

  # 合并配对数据 - 按患者ID匹配，并获取实际散点坐标
  paired_data <- baseline_samples %>%
    inner_join(post_samples, by = "Patient_ID") %>%
    filter(!is.na(Expression_Baseline) & !is.na(Expression_Post))

  cat(sprintf("找到 %d 个配对样本用于连线\n", nrow(paired_data)))

  if(nrow(paired_data) > 0) {
    # 获取实际散点的坐标
    baseline_coords <- combined_data %>%
      filter(Group_Label == "Baseline") %>%
      mutate(Patient_ID = gsub("--1$", "", Sample)) %>%
      select(Patient_ID, x_position, Expression) %>%
      rename(x1 = x_position, y1 = Expression)

    post_coords <- combined_data %>%
      filter(Group_Label == "Post-anti-VEGF") %>%
      mutate(Patient_ID = gsub("--2$", "", Sample)) %>%
      select(Patient_ID, x_position, Expression) %>%
      rename(x2 = x_position, y2 = Expression)

    # 合并坐标数据
    line_data <- baseline_coords %>%
      inner_join(post_coords, by = "Patient_ID") %>%
      filter(!is.na(x1) & !is.na(y1) & !is.na(x2) & !is.na(y2))

    cat(sprintf("准备绘制 %d 条连线\n", nrow(line_data)))

    # 添加连线
    if(nrow(line_data) > 0) {
      p <- p + geom_segment(data = line_data,
                           aes(x = x1, y = y1, xend = x2, yend = y2),
                           alpha = 0.4, color = "gray50", linewidth = 0.3,
                           inherit.aes = FALSE)

      cat("配对连线已添加\n")
    }
  } else {
    cat("未找到配对样本，跳过连线\n")
  }
  
  return(p)
}

cat("小提琴图函数创建完成\n")

# =============================================================================
# 7. 主要分析执行
# =============================================================================

cat("\n开始生成Figure 4...\n")

# 创建三个分子的小提琴图
violin_plots <- list()

# A. CAST
violin_plots$CAST <- create_violin_plot(target_molecules$CAST)

# B. GALNS
violin_plots$GALNS <- create_violin_plot(target_molecules$GALNS)

# C. 3-Hydroxypropanoic Acid
violin_plots$hydroxypropanoic <- create_violin_plot(target_molecules$hydroxypropanoic)

# 检查是否所有图都成功创建
valid_plots <- !sapply(violin_plots, is.null)
if(sum(valid_plots) == 0) {
  stop("错误：没有成功创建任何小提琴图")
}

# =============================================================================
# 8. 整合所有图表
# =============================================================================

cat("正在整合所有图表...\n")

# 创建图例
legend_data <- data.frame(
  Group = c("Simple VH", "SRD+VH", "Baseline", "Post-anti-VEGF"),
  x = 1:4,
  y = 1
)

legend_plot <- ggplot(legend_data, aes(x = x, y = y, fill = Group)) +
  geom_point(size = 4, shape = 21) +
  scale_fill_manual(values = c(
    "Simple VH" = colors$vh,
    "SRD+VH" = colors$srd_vh,
    "Baseline" = colors$baseline,
    "Post-anti-VEGF" = colors$post_vegf
  )) +
  theme_void() +
  theme(legend.position = "bottom",
        legend.direction = "horizontal",
        legend.title = element_text(size = 12, face = "bold"),
        legend.text = element_text(size = 11)) +
  guides(fill = guide_legend(title = "Group",
                            override.aes = list(size = 4),
                            nrow = 1))

# 提取图例
figure_legend <- get_legend(legend_plot)

# 组合有效的小提琴图
valid_plot_list <- violin_plots[valid_plots]

if(length(valid_plot_list) == 3) {
  # 所有三个图都成功创建
  combined_plots <- plot_grid(
    valid_plot_list$CAST,
    valid_plot_list$GALNS,
    valid_plot_list$hydroxypropanoic,
    labels = c("A", "B", "C"),
    label_size = 16,
    label_fontface = "bold",
    ncol = 3,
    rel_widths = c(1, 1, 1)
  )
} else if(length(valid_plot_list) == 2) {
  # 只有两个图成功创建
  combined_plots <- plot_grid(
    plotlist = valid_plot_list,
    labels = LETTERS[1:length(valid_plot_list)],
    label_size = 16,
    label_fontface = "bold",
    ncol = 2
  )
} else {
  # 只有一个图成功创建
  combined_plots <- valid_plot_list[[1]]
}

# 添加图例
final_plot <- plot_grid(
  combined_plots,
  figure_legend,
  ncol = 1,
  rel_heights = c(1, 0.1)
)

# 添加总标题
final_plot_with_title <- plot_grid(
  ggdraw() +
    draw_label("Aggravation Molecules: Baseline Comparison vs Longitudinal Changes",
               fontface = "bold", size = 18),
  final_plot,
  ncol = 1,
  rel_heights = c(0.05, 1)
)

# =============================================================================
# 9. 保存图表
# =============================================================================

cat("正在保存图表...\n")

# 保存高质量PNG
ggsave("Figure4_Violin_Plots.png",
       plot = final_plot_with_title,
       width = 16, height = 8,
       dpi = 300,
       bg = "white")

# 保存PDF
ggsave("Figure4_Violin_Plots.pdf",
       plot = final_plot_with_title,
       width = 16, height = 8,
       device = "pdf",
       bg = "white")

# =============================================================================
# 10. 生成分析报告
# =============================================================================

cat("\n生成分析报告...\n")

# 创建报告
report <- sprintf("
# Figure 4 分析报告 - R语言版本

## 数据概览
- 分析的分子数量: %d个
- 成功创建图表的分子: %d个
- 分子列表: %s

## 图表设计
### A. CAST (蛋白质)
- 基线比较: VH vs SRD+VH
- 纵向比较: 治疗前 vs 治疗后

### B. GALNS (蛋白质)
- 基线比较: VH vs SRD+VH
- 纵向比较: 治疗前 vs 治疗后

### C. 3-Hydroxypropanoic Acid (代谢物)
- 基线比较: VH vs SRD+VH
- 纵向比较: 治疗前 vs 治疗后

## 图表说明
- 每个分子包含4个小提琴图
- 前两个小提琴: 基线VH组 vs SRD+VH组比较
- 后两个小提琴: 25个病人治疗前后比较
- 小提琴图显示数据分布，箱线图显示四分位数，散点显示个体数据

## 输出文件
- Figure4_Violin_Plots.png (高质量PNG格式)
- Figure4_Violin_Plots.pdf (PDF格式)

## 分析完成时间
%s
",
length(target_molecules),
sum(valid_plots),
paste(names(target_molecules)[valid_plots], collapse = ", "),
Sys.time()
)

# 保存报告
writeLines(report, "Figure4_Analysis_Report.md")

cat("\n✅ Figure 4 分析完成！\n")
cat("📁 输出文件:\n")
cat("  - Figure4_Violin_Plots.png\n")
cat("  - Figure4_Violin_Plots.pdf\n")
cat("  - Figure4_Analysis_Report.md\n")

cat("\n🎉 R语言版本的Figure 4生成成功！\n")
