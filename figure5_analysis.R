#!/usr/bin/env Rscript
# Figure 5 Analysis Script - Violin Plots for Improvement Molecules
# 绘制35个Improvement物质的小提琴图，包括3个蛋白质和32个代谢物
# 排列为6行，每行6个图（最后一行5个图）
# 第一行：A1-A6，第二行：B1-B6，...，第六行：F1-F5
# 蛋白质用蓝色，代谢物用红色
#
# Author: AI Assistant
# Date: 2025-08-03
# Version: 1.0

# =============================================================================
# 1. 环境准备和包加载
# =============================================================================

# 检查并安装必要的包
required_packages <- c(
  "readxl",        # Excel文件读取
  "dplyr",         # 数据处理
  "tibble",        # 数据框操作
  "tidyr",         # 数据重塑
  "stringr",       # 字符串处理
  "ggplot2",       # 基础绘图
  "ggpubr",        # 发表质量图表
  "cowplot",       # 图表组合
  "viridis",       # 颜色方案
  "scales"         # 坐标轴格式化
)

# 安装缺失的包
new_packages <- required_packages[!(required_packages %in% installed.packages()[,"Package"])]
if(length(new_packages)) {
  install.packages(new_packages, repos = "https://cran.rstudio.com/")
}

# 加载包
suppressPackageStartupMessages({
  library(readxl)
  library(dplyr)
  library(tibble)
  library(tidyr)
  library(stringr)
  library(ggplot2)
  library(ggpubr)
  library(cowplot)
  library(viridis)
  library(scales)
})

# =============================================================================
# 2. 数据加载和预处理
# =============================================================================

cat("正在加载数据...\n")

# 加载基线数据 (Figure 1: VH vs SRD+VH)
baseline_protein_data <- read_excel("基线蛋白组学(1-stage2_vs_1-stage1_annotation).xlsx")
baseline_metabolite_data <- read_excel("基线代谢组学（1-stage2_vs_1-stage1_info）.xlsx")
baseline_group_data <- read_excel("基线分组情况（1-stage2_vs_1-stage1_group）.xlsx")

# 加载纵向数据 (Figure 2: Post-Anti-VEGF vs Baseline)
longitudinal_protein_data <- read_excel("纵向蛋白组学（2_vs_1_annotation）.xlsx")
longitudinal_metabolite_data <- read_excel("纵向代谢组学（2_vs_1_info）.xlsx")
longitudinal_group_data <- read_excel("纵向分组情况（2_vs_1_group）.xlsx")

cat(sprintf("数据加载完成:\n"))
cat(sprintf("- 基线蛋白质数据: %d行 x %d列\n", nrow(baseline_protein_data), ncol(baseline_protein_data)))
cat(sprintf("- 基线代谢物数据: %d行 x %d列\n", nrow(baseline_metabolite_data), ncol(baseline_metabolite_data)))
cat(sprintf("- 纵向蛋白质数据: %d行 x %d列\n", nrow(longitudinal_protein_data), ncol(longitudinal_protein_data)))
cat(sprintf("- 纵向代谢物数据: %d行 x %d列\n", nrow(longitudinal_metabolite_data), ncol(longitudinal_metabolite_data)))

# =============================================================================
# 3. 定义目标分子列表
# =============================================================================

# 定义35个improvement分子（先蛋白质，后代谢物）
improvement_molecules <- list(
  # 3个蛋白质
  proteins = list(
    list(name = "ALB", accession = "D6RHD5", gene = "ALB"),
    list(name = "ABHD12B", accession = "Q7Z5M8", gene = "ABHD12B"),
    list(name = "NIBAN3", accession = "Q86XR2", gene = "NIBAN3")
  ),
  # 32个代谢物
  metabolites = c(
    "L-threo-3-Methylaspartate",
    "L-Glutamic Acid",
    "Inosine",
    "3-Methyl-2-Oxobutanoic Acid",
    "3-(3-Hydroxyphenyl)-3-hydroxypropanoic acid",
    "3,4-Dimethoxycinnamic acid",
    "Pyridoxal phosphate",
    "Tryptamine",
    "Indoleacetaldehyde",
    "D-Ribono-1,4-lactone",
    "3-aminobenzamide",
    "8-Azaadenosine",
    "Acetylsulfamethoxazole",
    "(+)-7-iso-Jasmonic acid",
    "Isopalmitic acid",
    "9-[(3S,4S,5R)-3,4-dihydroxy-5-(hydroxymethyl)oxolan-2-yl]-1H-purin-6-one",
    "5-(3',4',5'-Trihydroxyphenyl)-gamma-valerolactone-3'-O-sulphate",
    "L-selenomethionine",
    "Methyl (methylthio)acetate",
    "Tyrosyl-tryptophan",
    "Hydroxyphenyllactic acid",
    "6-Cyano-7-nitroquinoxaline-2,3-dione",
    "Isosorbide mononitrate",
    "Lonidamine",
    "Thioctic acid",
    "Methyl 1-(1-propenylthio)propyl disulfide",
    "2,3-Epoxyaflatoxin B1",
    "(1E,4Z,6E)-5-hydroxy-1,7-bis(4-hydroxyphenyl)hepta-1,4,6-trien-3-one",
    "6,8a-Seco-6,8a-deoxy-5-oxoavermectin''1b'' aglycone",
    "Selenocysteine",
    "(S)-2-acetamido-6-oxopimelic acid",
    "Serotonin O-sulfate"
  )
)

cat(sprintf("目标分子定义完成: %d个蛋白质 + %d个代谢物 = %d个总分子\n", 
            length(improvement_molecules$proteins), 
            length(improvement_molecules$metabolites),
            length(improvement_molecules$proteins) + length(improvement_molecules$metabolites)))

# =============================================================================
# 4. 设置绘图主题和颜色
# =============================================================================

# 设置期刊发表质量的主题
theme_publication <- theme_bw() +
  theme(
    text = element_text(size = 10),
    axis.title = element_text(size = 11, face = "bold"),
    axis.text = element_text(size = 9),
    plot.title = element_text(size = 12, face = "bold", hjust = 0.5),
    legend.text = element_text(size = 9),
    legend.title = element_text(size = 10, face = "bold"),
    panel.grid.major = element_line(color = "grey90", linewidth = 0.3),
    panel.grid.minor = element_blank(),
    panel.border = element_rect(color = "black", linewidth = 0.8),
    strip.background = element_rect(fill = "white", color = "black"),
    strip.text = element_text(size = 10, face = "bold")
  )

cat("\n绘图环境设置完成\n")

# =============================================================================
# 5. 创建小提琴图函数
# =============================================================================

create_violin_plot <- function(molecule_data_baseline, molecule_data_longitudinal,
                               baseline_groups, longitudinal_groups,
                               molecule_name, molecule_type = "protein") {

  cat(sprintf("正在创建%s的小提琴图...\n", molecule_name))

  # 统计检验函数
  perform_t_test <- function(data, group_col, value_col, paired = FALSE) {
    if(paired) {
      # 配对t检验
      groups <- unique(data[[group_col]])
      group1_data <- data[data[[group_col]] == groups[1], value_col]
      group2_data <- data[data[[group_col]] == groups[2], value_col]
      if(length(group1_data) == length(group2_data) && length(group1_data) > 1) {
        test_result <- t.test(group1_data, group2_data, paired = TRUE)
        # 计算趋势：比较第二组相对于第一组的变化
        mean1 <- mean(group1_data, na.rm = TRUE)
        mean2 <- mean(group2_data, na.rm = TRUE)
        trend <- ifelse(mean2 > mean1, "Up", "Down")
        return(list(p_value = test_result$p.value, method = "Paired t-test",
                   trend = trend, mean1 = mean1, mean2 = mean2))
      }
    } else {
      # 独立t检验
      if(length(unique(data[[group_col]])) == 2) {
        test_result <- t.test(data[[value_col]] ~ data[[group_col]])
        # 计算趋势：比较两组均值
        groups <- unique(data[[group_col]])
        mean1 <- mean(data[data[[group_col]] == groups[1], value_col], na.rm = TRUE)
        mean2 <- mean(data[data[[group_col]] == groups[2], value_col], na.rm = TRUE)
        trend <- ifelse(mean2 > mean1, "Up", "Down")
        return(list(p_value = test_result$p.value, method = "Independent t-test",
                   trend = trend, mean1 = mean1, mean2 = mean2))
      }
    }
    return(list(p_value = NA, method = "No test", trend = "", mean1 = NA, mean2 = NA))
  }

  # 格式化P值和趋势
  format_p_value_with_trend <- function(p, trend) {
    if(is.na(p)) return("P = NA")
    p_text <- if(p < 0.001) "P < 0.001" else sprintf("P = %.3f", p)
    return(paste0(p_text, " (", trend, ")"))  # 格式: P = XXX (趋势)
  }

  # 获取样本列（包含"--"的列）
  baseline_sample_cols <- grep("--", colnames(molecule_data_baseline), value = TRUE)
  longitudinal_sample_cols <- grep("--", colnames(molecule_data_longitudinal), value = TRUE)

  # 准备基线数据（VH vs SRD+VH）
  baseline_values <- as.numeric(molecule_data_baseline[1, baseline_sample_cols])
  baseline_samples <- baseline_sample_cols
  baseline_group_info <- baseline_groups$Group[match(baseline_samples, baseline_groups$Sample)]

  baseline_df <- data.frame(
    Sample = baseline_samples,
    Value = baseline_values,
    Group = ifelse(baseline_group_info == "1-stage1", "Simple VH", "SRD+VH"),
    Comparison = "Baseline Comparison",
    stringsAsFactors = FALSE
  ) %>%
    filter(!is.na(Value) & !is.na(Group))

  # 准备纵向数据（治疗前后）
  # 提取治疗前（--1）和治疗后（--2）的数据
  pre_treatment_cols <- grep("--1$", longitudinal_sample_cols, value = TRUE)
  post_treatment_cols <- grep("--2$", longitudinal_sample_cols, value = TRUE)

  # 匹配治疗前后的样本
  pre_values <- as.numeric(molecule_data_longitudinal[1, pre_treatment_cols])
  post_values <- as.numeric(molecule_data_longitudinal[1, post_treatment_cols])

  # 创建患者ID匹配
  pre_patient_ids <- gsub("--1$", "", pre_treatment_cols)
  post_patient_ids <- gsub("--2$", "", post_treatment_cols)

  # 找到匹配的患者
  matched_patients <- intersect(pre_patient_ids, post_patient_ids)

  longitudinal_df <- data.frame()

  if(length(matched_patients) > 0) {
    for(patient in matched_patients) {
      pre_col <- paste0(patient, "--1")
      post_col <- paste0(patient, "--2")

      if(pre_col %in% pre_treatment_cols && post_col %in% post_treatment_cols) {
        pre_val <- as.numeric(molecule_data_longitudinal[1, pre_col])
        post_val <- as.numeric(molecule_data_longitudinal[1, post_col])

        if(!is.na(pre_val) && !is.na(post_val)) {
          longitudinal_df <- rbind(longitudinal_df,
            data.frame(
              Sample = c(pre_col, post_col),
              Value = c(pre_val, post_val),
              Group = c("Baseline", "Post-anti-VEGF"),
              Comparison = "Longitudinal Comparison",
              Patient = c(patient, patient),
              stringsAsFactors = FALSE
            )
          )
        }
      }
    }
  }

  # 合并数据
  plot_data <- rbind(
    baseline_df %>% select(Sample, Value, Group, Comparison),
    longitudinal_df %>% select(Sample, Value, Group, Comparison)
  )

  # 设置因子顺序
  plot_data$Group <- factor(plot_data$Group,
                           levels = c("Simple VH", "SRD+VH", "Baseline", "Post-anti-VEGF"))
  plot_data$Comparison <- factor(plot_data$Comparison,
                                levels = c("Baseline Comparison", "Longitudinal Comparison"))

  # 进行统计检验
  # 1. 基线比较：Simple VH vs SRD+VH (独立t检验)
  baseline_test_data <- plot_data %>%
    filter(Comparison == "Baseline Comparison" & !is.na(Value))
  baseline_test <- perform_t_test(baseline_test_data, "Group", "Value", paired = FALSE)

  # 2. 纵向比较：Baseline vs Post-anti-VEGF (配对t检验)
  longitudinal_test_data <- plot_data %>%
    filter(Comparison == "Longitudinal Comparison" & !is.na(Value))
  longitudinal_test <- perform_t_test(longitudinal_test_data, "Group", "Value", paired = TRUE)

  # 格式化P值和趋势文本
  baseline_p_text <- format_p_value_with_trend(baseline_test$p_value, baseline_test$trend)
  longitudinal_p_text <- format_p_value_with_trend(longitudinal_test$p_value, longitudinal_test$trend)

  # 准备配对连线数据（仅用于纵向比较）
  paired_data <- data.frame()
  if(nrow(longitudinal_df) > 0 && "Patient" %in% colnames(longitudinal_df)) {
    # 为每个患者生成一致的jitter位置
    set.seed(123)  # 确保jitter位置可重现
    paired_data <- longitudinal_df %>%
      filter(!is.na(Patient)) %>%
      select(Patient, Group, Value, Comparison) %>%
      group_by(Patient) %>%
      filter(n() == 2) %>%  # 确保每个患者都有两个时间点的数据
      ungroup() %>%
      arrange(Patient, Group) %>%
      group_by(Patient) %>%
      mutate(
        # 为每个患者生成一致的jitter偏移
        jitter_offset = runif(1, -0.2, 0.2),
        # 计算实际的x位置
        x_pos = case_when(
          Group == "Baseline" ~ 1 + jitter_offset,
          Group == "Post-anti-VEGF" ~ 2 + jitter_offset,
          TRUE ~ as.numeric(as.factor(Group)) + jitter_offset
        )
      ) %>%
      ungroup()
  }

  # 创建小提琴图
  p <- ggplot(plot_data, aes(x = Group, y = Value, fill = Group)) +
    geom_violin(alpha = 0.7, trim = FALSE) +
    geom_boxplot(width = 0.1, alpha = 0.8, outlier.shape = NA) +
    # 添加配对连线（仅在纵向比较面板中）
    {if(nrow(paired_data) > 0) {
      geom_line(data = paired_data %>%
                filter(Comparison == "Longitudinal Comparison"),
                aes(x = x_pos, y = Value, group = Patient),
                color = "black", alpha = 0.6, linewidth = 0.5, inherit.aes = FALSE)
    }} +
    # 修改散点样式：更大、有颜色填充、有黑色边框
    # 为纵向比较使用固定位置，为基线比较使用jitter
    {if(nrow(paired_data) > 0) {
      list(
        # 纵向比较的散点（固定位置，与连线对应）
        geom_point(data = paired_data %>% filter(Comparison == "Longitudinal Comparison"),
                   aes(x = x_pos, y = Value, fill = Group),
                   alpha = 0.8, size = 2, shape = 21, color = "black", stroke = 0.6,
                   inherit.aes = FALSE),
        # 基线比较的散点（使用jitter）
        geom_jitter(data = plot_data %>% filter(Comparison == "Baseline Comparison"),
                    aes(fill = Group), width = 0.2, alpha = 0.8, size = 2,
                    shape = 21, color = "black", stroke = 0.6)
      )
    } else {
      # 如果没有配对数据，所有散点都使用jitter
      geom_jitter(aes(fill = Group), width = 0.2, alpha = 0.8, size = 2,
                  shape = 21, color = "black", stroke = 0.6)
    }} +
    scale_fill_manual(values = {
      if(molecule_type == "protein") {
        # 蛋白质使用蓝色系
        c("Simple VH" = "#4472C4", "SRD+VH" = "#2F5597",
          "Baseline" = "#4472C4", "Post-anti-VEGF" = "#2F5597")
      } else {
        # 代谢物使用红色系
        c("Simple VH" = "#E74C3C", "SRD+VH" = "#C0392B",
          "Baseline" = "#E74C3C", "Post-anti-VEGF" = "#C0392B")
      }
    }) +
    facet_wrap(~ Comparison, scales = "free_x", ncol = 2) +
    labs(
      title = molecule_name,
      x = "",
      y = ifelse(molecule_type == "protein", "Protein Expression", "Metabolite Concentration"),
      fill = "Group"
    ) +
    theme_publication +
    theme(
      axis.text.x = element_text(angle = 45, hjust = 1, size = 8),
      legend.position = "none",  # 移除图例，因为颜色已经很清楚了
      strip.text = element_text(size = 9),
      plot.title = element_text(size = 10)
    )

  # 创建P值标注数据框
  p_value_data <- data.frame(
    Comparison = c("Baseline Comparison", "Longitudinal Comparison"),
    p_text = c(baseline_p_text, longitudinal_p_text),
    x_pos = c(1.5, 1.5),  # 在每个面板的中间位置
    stringsAsFactors = FALSE
  )

  # 添加P值标注
  p <- p +
    geom_text(data = p_value_data,
              aes(x = x_pos, y = Inf, label = p_text),
              vjust = 1.5, hjust = 0.5, size = 3, fontface = "bold",
              color = "black", inherit.aes = FALSE)

  return(p)
}

# =============================================================================
# 6. 分子查找函数
# =============================================================================

find_protein <- function(protein_info, baseline_data, longitudinal_data) {
  # 尝试多种方式查找蛋白质
  baseline_match <- baseline_data %>%
    filter(str_detect(Gene, protein_info$gene) |
           str_detect(Description, protein_info$gene) |
           str_detect(Accession, protein_info$accession))

  longitudinal_match <- longitudinal_data %>%
    filter(str_detect(Gene, protein_info$gene) |
           str_detect(Description, protein_info$gene) |
           str_detect(Accession, protein_info$accession))

  return(list(baseline = baseline_match, longitudinal = longitudinal_match))
}

find_metabolite <- function(metabolite_name, baseline_data, longitudinal_data) {
  # 尝试多种方式查找代谢物
  baseline_match <- baseline_data %>%
    filter(str_detect(Compounds, fixed(metabolite_name)))

  longitudinal_match <- longitudinal_data %>%
    filter(str_detect(Compounds, fixed(metabolite_name)))

  # 如果没找到，尝试部分匹配
  if(nrow(baseline_match) == 0) {
    # 提取关键词进行模糊匹配
    keywords <- strsplit(metabolite_name, "[-\\s,()]")[[1]]
    keywords <- keywords[nchar(keywords) > 3]  # 只保留长度大于3的关键词

    for(keyword in keywords) {
      baseline_match <- baseline_data %>%
        filter(str_detect(toupper(Compounds), toupper(keyword)))
      longitudinal_match <- longitudinal_data %>%
        filter(str_detect(toupper(Compounds), toupper(keyword)))
      if(nrow(baseline_match) > 0) break
    }
  }

  return(list(baseline = baseline_match, longitudinal = longitudinal_match))
}

# =============================================================================
# 7. 查找所有目标分子
# =============================================================================

cat("\n正在查找所有目标分子...\n")

# 存储所有找到的分子数据
found_molecules <- list()
molecule_labels <- c()

# 查找蛋白质
for(i in seq_along(improvement_molecules$proteins)) {
  protein_info <- improvement_molecules$proteins[[i]]
  cat(sprintf("查找蛋白质: %s (%s)...\n", protein_info$name, protein_info$gene))

  result <- find_protein(protein_info, baseline_protein_data, longitudinal_protein_data)

  if(nrow(result$baseline) > 0 && nrow(result$longitudinal) > 0) {
    found_molecules[[length(found_molecules) + 1]] <- list(
      name = protein_info$name,
      type = "protein",
      baseline_data = result$baseline,
      longitudinal_data = result$longitudinal
    )
    molecule_labels <- c(molecule_labels, protein_info$name)
    cat(sprintf("  ✓ 找到蛋白质: %s\n", protein_info$name))
  } else {
    cat(sprintf("  ✗ 未找到蛋白质: %s\n", protein_info$name))
  }
}

# 查找代谢物
for(metabolite_name in improvement_molecules$metabolites) {
  cat(sprintf("查找代谢物: %s...\n", metabolite_name))

  result <- find_metabolite(metabolite_name, baseline_metabolite_data, longitudinal_metabolite_data)

  if(nrow(result$baseline) > 0 && nrow(result$longitudinal) > 0) {
    found_molecules[[length(found_molecules) + 1]] <- list(
      name = metabolite_name,
      type = "metabolite",
      baseline_data = result$baseline,
      longitudinal_data = result$longitudinal
    )
    molecule_labels <- c(molecule_labels, metabolite_name)
    cat(sprintf("  ✓ 找到代谢物: %s\n", metabolite_name))
  } else {
    cat(sprintf("  ✗ 未找到代谢物: %s\n", metabolite_name))
  }
}

cat(sprintf("\n总共找到 %d 个分子（目标35个）\n", length(found_molecules)))

# =============================================================================
# 8. 创建所有小提琴图
# =============================================================================

cat("\n开始创建所有小提琴图...\n")

# 创建所有小提琴图
violin_plots <- list()

for(i in seq_along(found_molecules)) {
  molecule <- found_molecules[[i]]
  cat(sprintf("创建第%d个图: %s (%s)\n", i, molecule$name, molecule$type))

  tryCatch({
    plot <- create_violin_plot(
      molecule$baseline_data,
      molecule$longitudinal_data,
      baseline_group_data,
      longitudinal_group_data,
      molecule$name,
      molecule$type
    )
    violin_plots[[i]] <- plot
  }, error = function(e) {
    cat(sprintf("  ✗ 创建图表失败: %s - %s\n", molecule$name, e$message))
    # 创建占位图
    violin_plots[[i]] <- ggplot() +
      annotate("text", x = 0.5, y = 0.5, label = paste("Error:", molecule$name), size = 4) +
      labs(title = molecule$name) +
      theme_void()
  })
}

# 如果找到的分子少于35个，用占位图填充
while(length(violin_plots) < 35) {
  violin_plots[[length(violin_plots) + 1]] <- ggplot() +
    annotate("text", x = 0.5, y = 0.5, label = "Not Found", size = 4) +
    labs(title = paste("Molecule", length(violin_plots) + 1)) +
    theme_void()
}

# =============================================================================
# 9. 整合所有图表 - 6行布局
# =============================================================================

cat("\n正在整合所有图表...\n")

# 生成标签：A1-A6, B1-B6, C1-C6, D1-D6, E1-E6, F1-F5
generate_labels <- function(n_plots) {
  labels <- c()
  rows <- c("A", "B", "C", "D", "E", "F")

  for(i in 1:n_plots) {
    row_idx <- ceiling(i / 6)
    col_idx <- ((i - 1) %% 6) + 1
    if(row_idx <= length(rows)) {
      labels <- c(labels, paste0(rows[row_idx], col_idx))
    } else {
      labels <- c(labels, paste0("G", col_idx))  # 如果超过F行
    }
  }
  return(labels)
}

plot_labels <- generate_labels(length(violin_plots))

# 创建6行布局，每行6个图（最后一行5个图）
# 第1行：A1-A6 (图1-6)
row1 <- plot_grid(plotlist = violin_plots[1:6],
                  labels = plot_labels[1:6],
                  label_size = 12, ncol = 6)

# 第2行：B1-B6 (图7-12)
row2 <- plot_grid(plotlist = violin_plots[7:12],
                  labels = plot_labels[7:12],
                  label_size = 12, ncol = 6)

# 第3行：C1-C6 (图13-18)
row3 <- plot_grid(plotlist = violin_plots[13:18],
                  labels = plot_labels[13:18],
                  label_size = 12, ncol = 6)

# 第4行：D1-D6 (图19-24)
row4 <- plot_grid(plotlist = violin_plots[19:24],
                  labels = plot_labels[19:24],
                  label_size = 12, ncol = 6)

# 第5行：E1-E6 (图25-30)
row5 <- plot_grid(plotlist = violin_plots[25:30],
                  labels = plot_labels[25:30],
                  label_size = 12, ncol = 6)

# 第6行：F1-F5 (图31-35)
row6 <- plot_grid(plotlist = violin_plots[31:35],
                  labels = plot_labels[31:35],
                  label_size = 12, ncol = 5)

# 组合所有行
final_plot <- plot_grid(row1, row2, row3, row4, row5, row6,
                       ncol = 1, rel_heights = c(1, 1, 1, 1, 1, 1))

# 添加总标题
final_plot_with_title <- plot_grid(
  ggdraw() +
    draw_label("Figure 5: Improvement Molecules Expression Patterns",
               fontface = "bold", size = 20),
  final_plot,
  ncol = 1,
  rel_heights = c(0.03, 1)
)

# =============================================================================
# 10. 保存图表
# =============================================================================

cat("正在保存图表...\n")

# 保存高质量PNG
ggsave("Figure5_Violin_Plots.png",
       plot = final_plot_with_title,
       width = 24, height = 20,
       dpi = 300,
       bg = "white")

# 保存PDF
ggsave("Figure5_Violin_Plots.pdf",
       plot = final_plot_with_title,
       width = 24, height = 20,
       device = "pdf",
       bg = "white")

# =============================================================================
# 11. 生成分析报告
# =============================================================================

cat("\n生成分析报告...\n")

# 创建报告
report <- sprintf("
# Figure 5 分析报告 - Improvement分子小提琴图

## 数据概览
- 目标分子: 35个 (3个蛋白质 + 32个代谢物)
- 实际找到: %d个分子
- 基线样本数量: %d
- 纵向样本数量: %d

## 图表布局
- 总共6行，前5行每行6个图，最后一行5个图
- 第1行: A1-A6 (分子1-6)
- 第2行: B1-B6 (分子7-12)
- 第3行: C1-C6 (分子13-18)
- 第4行: D1-D6 (分子19-24)
- 第5行: E1-E6 (分子25-30)
- 第6行: F1-F5 (分子31-35)

## 颜色方案
- 蛋白质: 蓝色系
- 代谢物: 红色系

## 找到的分子列表
%s

## 输出文件
- Figure5_Violin_Plots.png (高质量PNG格式)
- Figure5_Violin_Plots.pdf (PDF格式)

## 分析完成时间
%s
",
length(found_molecules),
nrow(baseline_group_data),
nrow(longitudinal_group_data)/2,
paste(molecule_labels, collapse = "\n"),
Sys.time()
)

# 保存报告
writeLines(report, "Figure5_Analysis_Report.md")

cat("\n✅ Figure 5 分析完成！\n")
cat("📁 输出文件:\n")
cat("  - Figure5_Violin_Plots.png\n")
cat("  - Figure5_Violin_Plots.pdf\n")
cat("  - Figure5_Analysis_Report.md\n")

cat("\n🎉 Figure 5 小提琴图生成成功！\n")
